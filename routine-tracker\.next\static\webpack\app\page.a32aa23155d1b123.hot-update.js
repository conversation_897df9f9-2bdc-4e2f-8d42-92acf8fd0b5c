"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ChangePinPopup.tsx":
/*!*******************************************!*\
  !*** ./src/components/ChangePinPopup.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChangePinPopup: () => (/* binding */ ChangePinPopup)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ChangePinPopup auto */ \nvar _s = $RefreshSig$();\n\nfunction ChangePinPopup(param) {\n    let { onChangePin, onCancel, userName } = param;\n    _s();\n    const [currentPin, setCurrentPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newPin, setNewPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [confirmPin, setConfirmPin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError('');\n        if (currentPin.length !== 4) {\n            setError('Current PIN must be exactly 4 digits');\n            return;\n        }\n        if (!/^\\d{4}$/.test(currentPin)) {\n            setError('Current PIN must contain only numbers');\n            return;\n        }\n        if (newPin.length !== 4) {\n            setError('New PIN must be exactly 4 digits');\n            return;\n        }\n        if (!/^\\d{4}$/.test(newPin)) {\n            setError('New PIN must contain only numbers');\n            return;\n        }\n        if (newPin !== confirmPin) {\n            setError('New PINs do not match');\n            return;\n        }\n        if (currentPin === newPin) {\n            setError('New PIN must be different from current PIN');\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const result = await onChangePin(currentPin, newPin);\n            if (!result) {\n                setError('Current PIN is incorrect. Please try again.');\n            }\n        } catch (e) {\n            setError('Failed to change PIN. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-2xl\",\n                                children: \"\\uD83D\\uDD10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-bold text-gray-900 mb-2\",\n                            children: \"Change Your PIN\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"Hi \",\n                                userName,\n                                \"! Enter your current PIN and set a new 4-digit PIN.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600 text-sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSubmit,\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"currentPin\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Current PIN\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    id: \"currentPin\",\n                                    value: currentPin,\n                                    onChange: (e)=>setCurrentPin(e.target.value.slice(0, 4)),\n                                    placeholder: \"••••\",\n                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest\",\n                                    maxLength: 4,\n                                    pattern: \"[0-9]{4}\",\n                                    disabled: isLoading,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"newPin\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"New PIN\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    id: \"newPin\",\n                                    value: newPin,\n                                    onChange: (e)=>setNewPin(e.target.value.slice(0, 4)),\n                                    placeholder: \"••••\",\n                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest\",\n                                    maxLength: 4,\n                                    pattern: \"[0-9]{4}\",\n                                    disabled: isLoading,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"confirmNewPin\",\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Confirm New PIN\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"password\",\n                                    id: \"confirmNewPin\",\n                                    value: confirmPin,\n                                    onChange: (e)=>setConfirmPin(e.target.value.slice(0, 4)),\n                                    placeholder: \"••••\",\n                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors text-center text-2xl tracking-widest\",\n                                    maxLength: 4,\n                                    pattern: \"[0-9]{4}\",\n                                    disabled: isLoading,\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: onCancel,\n                                    disabled: isLoading,\n                                    className: \"flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || !currentPin || !newPin || !confirmPin,\n                                    className: \"flex-1 bg-indigo-600 text-white py-3 px-4 rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Changing...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this) : 'Change PIN 🔒'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-700 text-xs\",\n                        children: \"\\uD83D\\uDD12 Your PIN is securely stored and protects your routine data from unauthorized access.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n            lineNumber: 67,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\ChangePinPopup.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n_s(ChangePinPopup, \"CN/fnccWbTdv/1ElEQuc4VmC5mo=\");\n_c = ChangePinPopup;\nvar _c;\n$RefreshReg$(_c, \"ChangePinPopup\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChangePinPopup.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/RoutineTracker.tsx":
/*!*******************************************!*\
  !*** ./src/components/RoutineTracker.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoutineTracker: () => (/* binding */ RoutineTracker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* harmony import */ var _WeeklyReport__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./WeeklyReport */ \"(app-pages-browser)/./src/components/WeeklyReport.tsx\");\n/* harmony import */ var _TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./TaskInfoPopup */ \"(app-pages-browser)/./src/components/TaskInfoPopup.tsx\");\n/* harmony import */ var _PinSetupPopup__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PinSetupPopup */ \"(app-pages-browser)/./src/components/PinSetupPopup.tsx\");\n/* harmony import */ var _ChangePinPopup__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ChangePinPopup */ \"(app-pages-browser)/./src/components/ChangePinPopup.tsx\");\n/* __next_internal_client_entry_do_not_use__ RoutineTracker auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction RoutineTracker(param) {\n    let { userId, onLogout } = param;\n    _s();\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showWeeklyReport, setShowWeeklyReport] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [todayRoutineItems, setTodayRoutineItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n    const [selectedTask, setSelectedTask] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPinSetup, setShowPinSetup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            initializeTracker();\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        userId\n    ]);\n    // Update routine items when day changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const updateRoutineItems = {\n                \"RoutineTracker.useEffect.updateRoutineItems\": ()=>{\n                    setTodayRoutineItems((0,_types__WEBPACK_IMPORTED_MODULE_2__.getTodayRoutineItems)());\n                }\n            }[\"RoutineTracker.useEffect.updateRoutineItems\"];\n            // Update immediately\n            updateRoutineItems();\n            // Set up interval to check for day change every minute\n            const interval = setInterval({\n                \"RoutineTracker.useEffect.interval\": ()=>{\n                    updateRoutineItems();\n                }\n            }[\"RoutineTracker.useEffect.interval\"], 60000); // Check every minute\n            return ({\n                \"RoutineTracker.useEffect\": ()=>clearInterval(interval)\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], []);\n    const initializeTracker = async ()=>{\n        setIsLoading(true);\n        try {\n            // Check for daily reset\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.checkAndPerformDailyReset)(userId);\n            // Get user info\n            const users = JSON.parse(localStorage.getItem('routine_tracker_users') || '[]');\n            const currentUser = users.find((u)=>u.id === userId);\n            setUser(currentUser);\n            // Get today's progress\n            const todayProgress = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getTodayProgress)(userId);\n            if (todayProgress) {\n                setProgress(todayProgress);\n            } else {\n                // Create new progress for today\n                const newProgress = {\n                    userId,\n                    date: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentDate)(),\n                    completedItems: [],\n                    lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                setProgress(newProgress);\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(newProgress);\n            }\n            // Check if user needs PIN setup (only show once)\n            if (!(0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.userHasPin)(userId)) {\n                // Show PIN setup popup after a short delay\n                setTimeout(()=>{\n                    setShowPinSetup(true);\n                }, 1000);\n            }\n        } catch (error) {\n            console.error('Error initializing tracker:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleItem = async (itemId)=>{\n        if (!progress || isSaving) return;\n        setIsSaving(true);\n        try {\n            const updatedItems = progress.completedItems.includes(itemId) ? progress.completedItems.filter((id)=>id !== itemId) : [\n                ...progress.completedItems,\n                itemId\n            ];\n            const updatedProgress = {\n                ...progress,\n                completedItems: updatedItems,\n                lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n            };\n            setProgress(updatedProgress);\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(updatedProgress);\n        } catch (error) {\n            console.error('Error updating progress:', error);\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleLogout = ()=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)('');\n        onLogout();\n    };\n    const toggleMenu = ()=>{\n        setShowMenu(!showMenu);\n    };\n    const openWeeklyReport = ()=>{\n        setShowWeeklyReport(true);\n        setShowMenu(false);\n    };\n    const closeWeeklyReport = ()=>{\n        setShowWeeklyReport(false);\n    };\n    const handleSetPin = (pin)=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setPinForUser)(userId, pin);\n        setShowPinSetup(false);\n    };\n    const handleSkipPin = ()=>{\n        setShowPinSetup(false);\n    };\n    const openTaskInfo = (task)=>{\n        setSelectedTask(task);\n    };\n    const closeTaskInfo = ()=>{\n        setSelectedTask(null);\n    };\n    // Close menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"RoutineTracker.useEffect.handleClickOutside\": (event)=>{\n                    if (showMenu) {\n                        const target = event.target;\n                        if (!target.closest('.relative')) {\n                            setShowMenu(false);\n                        }\n                    }\n                }\n            }[\"RoutineTracker.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"RoutineTracker.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"RoutineTracker.useEffect\"];\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        showMenu\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/icon.png\",\n                        alt: \"Routine Tracker\",\n                        className: \"w-16 h-16 mx-auto mb-4 rounded-lg shadow-md\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-2\",\n                        children: \"Loading your tracker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-indigo-600 font-semibold\",\n                        children: \"Built with ❤️ by Tech Talk\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    if (!progress) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: \"Error loading your progress. Please try again.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeTracker,\n                        className: \"mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 204,\n            columnNumber: 7\n        }, this);\n    }\n    const completionRate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.calculateCompletionRate)(progress.completedItems, todayRoutineItems.length);\n    const currentDate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.getNigerianTimeDisplay)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-start mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: \"/icon.png\",\n                                            alt: \"Routine Tracker\",\n                                            className: \"w-12 h-12 rounded-lg shadow-md\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: [\n                                                        \"Welcome back, \",\n                                                        (user === null || user === void 0 ? void 0 : user.name) || 'Friend',\n                                                        \"! \\uD83D\\uDC4B\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: currentDate\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: toggleMenu,\n                                                className: \"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\",\n                                                title: \"Menu\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-6 h-6\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: \"2\",\n                                                        d: \"M4 6h16M4 12h16M4 18h16\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this),\n                                            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: openWeeklyReport,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDCCA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Weekly Report\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-t border-gray-100 my-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleLogout,\n                                                        className: \"w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDC64\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Switch User\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        showPinChangeSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-2xl\",\n                                        children: \"✅\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-800 font-medium\",\n                                            children: \"PIN Changed Successfully!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-green-600 text-sm\",\n                                            children: \"Your account is now secured with your new PIN.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Todays Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        progress.completedItems.length,\n                                                        \"/\",\n                                                        todayRoutineItems.length\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Completion Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        completionRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-indigo-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(completionRate, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-6\",\n                            children: \"Daily Routine Checklist ✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: todayRoutineItems.map((item)=>{\n                                const isCompleted = progress.completedItems.includes(item.id);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer \".concat(isCompleted ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50'),\n                                    onClick: ()=>toggleItem(item.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium \".concat(isCompleted ? 'text-green-800' : 'text-gray-800'),\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm \".concat(isCompleted ? 'text-green-600' : 'text-gray-600'),\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                item.detailedInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        openTaskInfo(item);\n                                                    },\n                                                    className: \"w-6 h-6 rounded-full bg-indigo-100 hover:bg-indigo-200 flex items-center justify-center transition-colors\",\n                                                    title: \"More info\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-indigo-600 text-sm font-bold\",\n                                                        children: \"ⓘ\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 360,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-6 h-6 rounded-full border-2 flex items-center justify-center \".concat(isCompleted ? 'border-green-500 bg-green-500' : 'border-gray-300'),\n                                                    children: isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-white\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center text-sm text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Saving...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: completionRate === 100 ? \"🎉 Amazing! Youve completed all your routines today!\" : completionRate >= 75 ? \"🔥 Youre doing great! Keep it up!\" : completionRate >= 50 ? \"💪 Good progress! Youre halfway there!\" : \"🌱 Every step counts. Youve got this!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: \"Progress auto-saves • Resets at midnight • Access Weekly Report from menu\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2\",\n                            children: [\n                                \"Built with ❤️ by \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-indigo-600\",\n                                    children: \"Tech Talk\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 30\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 9\n                }, this),\n                showWeeklyReport && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WeeklyReport__WEBPACK_IMPORTED_MODULE_4__.WeeklyReport, {\n                    userId: userId,\n                    onClose: closeWeeklyReport\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 11\n                }, this),\n                selectedTask && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TaskInfoPopup__WEBPACK_IMPORTED_MODULE_5__.TaskInfoPopup, {\n                    item: selectedTask,\n                    onClose: closeTaskInfo\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 420,\n                    columnNumber: 11\n                }, this),\n                showPinSetup && user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PinSetupPopup__WEBPACK_IMPORTED_MODULE_6__.PinSetupPopup, {\n                    userName: user.name,\n                    onSetPin: handleSetPin,\n                    onSkip: handleSkipPin\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 425,\n                    columnNumber: 11\n                }, this),\n                showChangePinPopup && user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChangePinPopup__WEBPACK_IMPORTED_MODULE_7__.ChangePinPopup, {\n                    userName: user.name,\n                    onChangePin: handleChangePin,\n                    onCancel: handleCancelChangePin\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutineTracker, \"6zdwML8RTS8jYarve9NpMnnmxzQ=\");\n_c = RoutineTracker;\nvar _c;\n$RefreshReg$(_c, \"RoutineTracker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/RoutineTracker.tsx\n"));

/***/ })

});