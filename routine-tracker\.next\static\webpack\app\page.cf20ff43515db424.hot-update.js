"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/utils/storage.ts":
/*!******************************!*\
  !*** ./src/utils/storage.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateStreak: () => (/* binding */ calculateStreak),\n/* harmony export */   changePinForUser: () => (/* binding */ changePinForUser),\n/* harmony export */   checkAndPerformDailyReset: () => (/* binding */ checkAndPerformDailyReset),\n/* harmony export */   clearAllData: () => (/* binding */ clearAllData),\n/* harmony export */   forceResetForTesting: () => (/* binding */ forceResetForTesting),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getDailyProgress: () => (/* binding */ getDailyProgress),\n/* harmony export */   getHistoricalData: () => (/* binding */ getHistoricalData),\n/* harmony export */   getTodayProgress: () => (/* binding */ getTodayProgress),\n/* harmony export */   getUserByName: () => (/* binding */ getUserByName),\n/* harmony export */   getUserHistoricalData: () => (/* binding */ getUserHistoricalData),\n/* harmony export */   getUserProgress: () => (/* binding */ getUserProgress),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   hashPin: () => (/* binding */ hashPin),\n/* harmony export */   saveDailyProgress: () => (/* binding */ saveDailyProgress),\n/* harmony export */   saveHistoricalData: () => (/* binding */ saveHistoricalData),\n/* harmony export */   saveUser: () => (/* binding */ saveUser),\n/* harmony export */   setCurrentUser: () => (/* binding */ setCurrentUser),\n/* harmony export */   setPinForUser: () => (/* binding */ setPinForUser),\n/* harmony export */   userHasPin: () => (/* binding */ userHasPin),\n/* harmony export */   verifyPin: () => (/* binding */ verifyPin)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n// Local storage utilities for the Digital Routine & Results Tracker\n\nconst STORAGE_KEYS = {\n    USERS: 'routine_tracker_users',\n    DAILY_PROGRESS: 'routine_tracker_daily_progress',\n    HISTORICAL_DATA: 'routine_tracker_historical_data',\n    CURRENT_USER: 'routine_tracker_current_user'\n};\n// User management\nconst saveUser = (user)=>{\n    if (false) {}\n    const users = getUsers();\n    const existingIndex = users.findIndex((u)=>u.id === user.id);\n    if (existingIndex >= 0) {\n        users[existingIndex] = user;\n    } else {\n        users.push(user);\n    }\n    localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));\n};\nconst getUsers = ()=>{\n    if (false) {}\n    const stored = localStorage.getItem(STORAGE_KEYS.USERS);\n    return stored ? JSON.parse(stored) : [];\n};\nconst getUserByName = (name)=>{\n    const users = getUsers();\n    return users.find((u)=>u.name.toLowerCase() === name.toLowerCase()) || null;\n};\nconst setCurrentUser = (userId)=>{\n    if (false) {}\n    localStorage.setItem(STORAGE_KEYS.CURRENT_USER, userId);\n};\nconst getCurrentUser = ()=>{\n    if (false) {}\n    return localStorage.getItem(STORAGE_KEYS.CURRENT_USER);\n};\n// PIN management functions\nconst hashPin = (pin)=>{\n    // Simple hash function for PIN (in production, use a proper hashing library)\n    let hash = 0;\n    for(let i = 0; i < pin.length; i++){\n        const char = pin.charCodeAt(i);\n        hash = (hash << 5) - hash + char;\n        hash = hash & hash; // Convert to 32-bit integer\n    }\n    return Math.abs(hash).toString();\n};\nconst setPinForUser = (userId, pin)=>{\n    const users = getUsers();\n    const userIndex = users.findIndex((u)=>u.id === userId);\n    if (userIndex !== -1) {\n        users[userIndex].pin = hashPin(pin);\n        users[userIndex].hasPinSetup = true;\n        localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));\n    }\n};\nconst verifyPin = (userId, pin)=>{\n    const users = getUsers();\n    const user = users.find((u)=>u.id === userId);\n    if (!user || !user.pin) return false;\n    return user.pin === hashPin(pin);\n};\nconst userHasPin = (userId)=>{\n    const users = getUsers();\n    const user = users.find((u)=>u.id === userId);\n    return !!(user && user.hasPinSetup);\n};\nconst changePinForUser = (userId, currentPin, newPin)=>{\n    // First verify the current PIN\n    if (!verifyPin(userId, currentPin)) {\n        return false; // Current PIN is incorrect\n    }\n    // If current PIN is correct, set the new PIN\n    const users = getUsers();\n    const userIndex = users.findIndex((u)=>u.id === userId);\n    if (userIndex !== -1) {\n        users[userIndex].pin = hashPin(newPin);\n        users[userIndex].hasPinSetup = true;\n        localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));\n        return true; // PIN changed successfully\n    }\n    return false; // User not found\n};\n// Daily progress management\nconst saveDailyProgress = (progress)=>{\n    if (false) {}\n    const allProgress = getDailyProgress();\n    const key = \"\".concat(progress.userId, \"_\").concat(progress.date);\n    allProgress[key] = progress;\n    localStorage.setItem(STORAGE_KEYS.DAILY_PROGRESS, JSON.stringify(allProgress));\n};\nconst getDailyProgress = ()=>{\n    if (false) {}\n    const stored = localStorage.getItem(STORAGE_KEYS.DAILY_PROGRESS);\n    return stored ? JSON.parse(stored) : {};\n};\nconst getTodayProgress = (userId)=>{\n    const allProgress = getDailyProgress();\n    const key = \"\".concat(userId, \"_\").concat((0,_types__WEBPACK_IMPORTED_MODULE_0__.getCurrentDate)());\n    return allProgress[key] || null;\n};\nconst getUserProgress = (userId, date)=>{\n    const allProgress = getDailyProgress();\n    const key = \"\".concat(userId, \"_\").concat(date);\n    return allProgress[key] || null;\n};\n// Historical data management\nconst saveHistoricalData = (data)=>{\n    if (false) {}\n    const allHistorical = getHistoricalData();\n    const key = \"\".concat(data.userId, \"_\").concat(data.date);\n    allHistorical[key] = data;\n    localStorage.setItem(STORAGE_KEYS.HISTORICAL_DATA, JSON.stringify(allHistorical));\n};\nconst getHistoricalData = ()=>{\n    if (false) {}\n    const stored = localStorage.getItem(STORAGE_KEYS.HISTORICAL_DATA);\n    return stored ? JSON.parse(stored) : {};\n};\nconst getUserHistoricalData = function(userId) {\n    let days = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 30;\n    const allHistorical = getHistoricalData();\n    const userHistorical = [];\n    // Get last N days of data\n    for(let i = 0; i < days; i++){\n        const date = new Date();\n        date.setDate(date.getDate() - i);\n        const dateStr = date.toISOString().split('T')[0];\n        const key = \"\".concat(userId, \"_\").concat(dateStr);\n        if (allHistorical[key]) {\n            userHistorical.push(allHistorical[key]);\n        }\n    }\n    return userHistorical.sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime());\n};\n// Daily reset logic\nconst checkAndPerformDailyReset = (userId)=>{\n    const currentDate = (0,_types__WEBPACK_IMPORTED_MODULE_0__.getCurrentDate)();\n    const todayProgress = getTodayProgress(userId);\n    // Check if we have progress data and if it's from a previous day\n    if (todayProgress && todayProgress.date !== currentDate) {\n        // Archive the old progress to historical data\n        const historicalData = {\n            userId: todayProgress.userId,\n            date: todayProgress.date,\n            completedItems: todayProgress.completedItems,\n            completionRate: Math.round(todayProgress.completedItems.length / 8 * 100),\n            streak: calculateStreak(userId, todayProgress.date)\n        };\n        saveHistoricalData(historicalData);\n        // Clear the old progress data by removing it from daily progress\n        const allProgress = getDailyProgress();\n        const oldKey = \"\".concat(userId, \"_\").concat(todayProgress.date);\n        delete allProgress[oldKey];\n        localStorage.setItem(STORAGE_KEYS.DAILY_PROGRESS, JSON.stringify(allProgress));\n        return true; // Reset needed - old data archived and cleared\n    }\n    // If no progress exists for today, or progress is already for today, no reset needed\n    return false;\n};\n// Calculate current streak\nconst calculateStreak = (userId, endDate)=>{\n    let streak = 0;\n    const date = new Date(endDate);\n    while(true){\n        const dateStr = date.toISOString().split('T')[0];\n        const progress = getUserProgress(userId, dateStr);\n        if (progress && progress.completedItems.length > 0) {\n            streak++;\n            date.setDate(date.getDate() - 1);\n        } else {\n            break;\n        }\n    }\n    return streak;\n};\n// Clear all data (for testing/reset)\nconst clearAllData = ()=>{\n    if (false) {}\n    Object.values(STORAGE_KEYS).forEach((key)=>{\n        localStorage.removeItem(key);\n    });\n};\n// Force daily reset for testing (simulates day change)\nconst forceResetForTesting = (userId)=>{\n    if (false) {}\n    const todayProgress = getTodayProgress(userId);\n    if (todayProgress) {\n        // Change the date to yesterday to trigger reset\n        const yesterday = new Date();\n        yesterday.setDate(yesterday.getDate() - 1);\n        const yesterdayStr = yesterday.toISOString().split('T')[0];\n        // Update the progress date to yesterday\n        const allProgress = getDailyProgress();\n        const todayKey = \"\".concat(userId, \"_\").concat((0,_types__WEBPACK_IMPORTED_MODULE_0__.getCurrentDate)());\n        const yesterdayKey = \"\".concat(userId, \"_\").concat(yesterdayStr);\n        if (allProgress[todayKey]) {\n            allProgress[yesterdayKey] = {\n                ...allProgress[todayKey],\n                date: yesterdayStr\n            };\n            delete allProgress[todayKey];\n            localStorage.setItem(STORAGE_KEYS.DAILY_PROGRESS, JSON.stringify(allProgress));\n        }\n    }\n    console.log('Reset simulation complete. Refresh the page to see the reset effect.');\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/storage.ts\n"));

/***/ })

});