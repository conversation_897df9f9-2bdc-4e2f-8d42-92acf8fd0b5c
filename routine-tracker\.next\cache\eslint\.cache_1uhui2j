[{"C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\RoutineTracker.tsx": "3", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\TaskInfoPopup.tsx": "4", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WeeklyReport.tsx": "5", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WelcomeScreen.tsx": "6", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\config\\auth.ts": "7", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\types\\index.ts": "8", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\realtime.ts": "9", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\storage.ts": "10", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinInputScreen.tsx": "11", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinSetupPopup.tsx": "12", "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\ChangePinPopup.tsx": "13"}, {"size": 1366, "mtime": 1752274889994, "results": "14", "hashOfConfig": "15"}, {"size": 3121, "mtime": 1752250512136, "results": "16", "hashOfConfig": "15"}, {"size": 14914, "mtime": 1752275269316, "results": "17", "hashOfConfig": "15"}, {"size": 4791, "mtime": 1752158286771, "results": "18", "hashOfConfig": "15"}, {"size": 10123, "mtime": 1752166006347, "results": "19", "hashOfConfig": "15"}, {"size": 6205, "mtime": 1752250227082, "results": "20", "hashOfConfig": "15"}, {"size": 1918, "mtime": 1752157934473, "results": "21", "hashOfConfig": "15"}, {"size": 12024, "mtime": 1752250357146, "results": "22", "hashOfConfig": "15"}, {"size": 5172, "mtime": 1752187602092, "results": "23", "hashOfConfig": "15"}, {"size": 7681, "mtime": 1752275199803, "results": "24", "hashOfConfig": "15"}, {"size": 5321, "mtime": 1752251478628, "results": "25", "hashOfConfig": "15"}, {"size": 5259, "mtime": 1752273139946, "results": "26", "hashOfConfig": "15"}, {"size": 6293, "mtime": 1752273645188, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "9cy87n", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\app\\page.tsx", ["67"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\RoutineTracker.tsx", ["68", "69", "70", "71"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\TaskInfoPopup.tsx", ["72", "73"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WeeklyReport.tsx", ["74", "75"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\WelcomeScreen.tsx", ["76"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\config\\auth.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\realtime.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\utils\\storage.ts", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinInputScreen.tsx", ["77", "78"], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\PinSetupPopup.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\archive-2025-06-05T142720+0200\\Practice\\Rr 1.0\\routine-tracker\\src\\components\\ChangePinPopup.tsx", [], [], {"ruleId": "79", "severity": 1, "message": "80", "line": 68, "column": 11, "nodeType": "81", "endLine": 72, "endColumn": 13}, {"ruleId": "82", "severity": 1, "message": "83", "line": 43, "column": 6, "nodeType": "84", "endLine": 43, "endColumn": 14, "suggestions": "85"}, {"ruleId": "82", "severity": 1, "message": "83", "line": 74, "column": 6, "nodeType": "84", "endLine": 74, "endColumn": 14, "suggestions": "86"}, {"ruleId": "79", "severity": 1, "message": "80", "line": 200, "column": 11, "nodeType": "81", "endLine": 204, "endColumn": 13}, {"ruleId": "79", "severity": 1, "message": "80", "line": 241, "column": 15, "nodeType": "81", "endLine": 245, "endColumn": 17}, {"ruleId": "82", "severity": 1, "message": "87", "line": 31, "column": 6, "nodeType": "84", "endLine": 31, "endColumn": 8, "suggestions": "88"}, {"ruleId": "82", "severity": 1, "message": "87", "line": 46, "column": 6, "nodeType": "84", "endLine": 46, "endColumn": 8, "suggestions": "89"}, {"ruleId": "82", "severity": 1, "message": "90", "line": 26, "column": 6, "nodeType": "84", "endLine": 26, "endColumn": 28, "suggestions": "91"}, {"ruleId": "79", "severity": 1, "message": "80", "line": 115, "column": 13, "nodeType": "81", "endLine": 119, "endColumn": 15}, {"ruleId": "79", "severity": 1, "message": "80", "line": 70, "column": 11, "nodeType": "81", "endLine": 74, "endColumn": 13}, {"ruleId": "82", "severity": 1, "message": "92", "line": 21, "column": 6, "nodeType": "84", "endLine": 21, "endColumn": 11, "suggestions": "93"}, {"ruleId": "79", "severity": 1, "message": "80", "line": 55, "column": 11, "nodeType": "81", "endLine": 59, "endColumn": 13}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeTracker'. Either include it or remove the dependency array.", "ArrayExpression", ["94"], ["95"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["96"], ["97"], "React Hook useEffect has a missing dependency: 'loadWeeklyData'. Either include it or remove the dependency array.", ["98"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["99"], {"desc": "100", "fix": "101"}, {"desc": "100", "fix": "102"}, {"desc": "103", "fix": "104"}, {"desc": "103", "fix": "105"}, {"desc": "106", "fix": "107"}, {"desc": "108", "fix": "109"}, "Update the dependencies array to be: [initializeTracker, userId]", {"range": "110", "text": "111"}, {"range": "112", "text": "111"}, "Update the dependencies array to be: [handleClose]", {"range": "113", "text": "114"}, {"range": "115", "text": "114"}, "Update the dependencies array to be: [userId, selectedWeek, loadWeeklyData]", {"range": "116", "text": "117"}, "Update the dependencies array to be: [handleSubmit, pin]", {"range": "118", "text": "119"}, [1334, 1342], "[initializeTracker, userId]", [2219, 2227], [816, 818], "[handleClose]", [1150, 1152], [729, 751], "[userId, selectedWeek, loadWeeklyData]", [506, 511], "[handleSubmit, pin]"]